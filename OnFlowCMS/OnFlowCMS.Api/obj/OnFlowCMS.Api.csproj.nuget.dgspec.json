{"format": 1, "restore": {"/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Api/OnFlowCMS.Api.csproj": {}}, "projects": {"/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Api/OnFlowCMS.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Api/OnFlowCMS.Api.csproj", "projectName": "OnFlowCMS.Api", "projectPath": "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Api/OnFlowCMS.Api.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Api/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Application/OnFlowCMS.Application.csproj": {"projectPath": "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Application/OnFlowCMS.Application.csproj"}, "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Infrastructure/OnFlowCMS.Infrastructure.csproj": {"projectPath": "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Infrastructure/OnFlowCMS.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.1, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.AspNetCore.Diagnostics.HealthChecks": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Mvc.Versioning": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.8, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[7.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[9.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Application/OnFlowCMS.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Application/OnFlowCMS.Application.csproj", "projectName": "OnFlowCMS.Application", "projectPath": "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Application/OnFlowCMS.Application.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Application/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Domain/OnFlowCMS.Domain.csproj": {"projectPath": "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Domain/OnFlowCMS.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[15.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[12.0.0, )"}, "MediatR": {"target": "Package", "version": "[13.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.8, )"}, "Slugify.Core": {"target": "Package", "version": "[5.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Domain/OnFlowCMS.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Domain/OnFlowCMS.Domain.csproj", "projectName": "OnFlowCMS.Domain", "projectPath": "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Domain/OnFlowCMS.Domain.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Domain/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.8, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Infrastructure/OnFlowCMS.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Infrastructure/OnFlowCMS.Infrastructure.csproj", "projectName": "OnFlowCMS.Infrastructure", "projectPath": "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Infrastructure/OnFlowCMS.Infrastructure.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Infrastructure/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Application/OnFlowCMS.Application.csproj": {"projectPath": "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Application/OnFlowCMS.Application.csproj"}, "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Domain/OnFlowCMS.Domain.csproj": {"projectPath": "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Domain/OnFlowCMS.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"EFCore.NamingConventions": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.8, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[8.2.1, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}, "Slugify.Core": {"target": "Package", "version": "[5.1.1, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.2.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/9.0.304/PortableRuntimeIdentifierGraph.json"}}}}}