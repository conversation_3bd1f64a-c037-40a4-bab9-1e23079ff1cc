using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using OnFlowCMS.Application.Interfaces;
using OnFlowCMS.Application.DTOs.Account;
using OnFlowCMS.Domain.Enums;
using OnFlowCMS.Infrastructure.Identity;

namespace OnFlowCMS.Api.Controllers.v1;

[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
public class AuthController : ControllerBase
{
    private readonly UserManager<AppUser> _userManager;
    private readonly SignInManager<AppUser> _signInManager;
    private readonly IJwtService _jwtService;
    private readonly IConfiguration _config;

    public AuthController(UserManager<AppUser> userManager, SignInManager<AppUser> signInManager, IJwtService jwtService, IConfiguration config)
    {
        _userManager = userManager;
        _signInManager = signInManager;
        _jwtService = jwtService;
        _config = config;
    }

    [HttpPost("signup")]
    public async Task<IActionResult> Register([FromBody] AuthRequestDto request)
    {
        var user = new AppUser
        {
            Email = request.Email,
            Role = UserRole.Reader
        };

        var result = await _userManager.CreateAsync(user, request.Password);

        if (!result.Succeeded) return BadRequest(result.Errors);

        var token = _jwtService.GenerateToken(user.Id, user.Email, user.Role.ToString());

        var refreshToken = _jwtService.GenerateRefreshToken();

        user.RefreshToken = refreshToken;
        user.RefreshTokenExpiry = DateTime.UtcNow.AddSeconds(int.Parse(_config["Jwt:RefreshTokenTtlSeconds"] ?? "604800"));

        await _userManager.UpdateAsync(user);

        return Ok(new AuthResponseDto
        {
            User = new RequestUserDto
            {
                Id = user.Id,
                Email = user.Email!,
                Role = user.Role.ToString()
            },
            Token = token,
            RefreshToken = refreshToken
        });
    }

    [HttpPost("signin")]
    public async Task<IActionResult> Login([FromBody] AuthRequestDto request)
    {
        // Find user by email
        var user = await _userManager.FindByEmailAsync(request.Email);
        if (user == null)
        {
            // Use consistent error message to prevent email enumeration
            return Unauthorized("Invalid credentials");
        }

        // Check if account is locked
        if (await _userManager.IsLockedOutAsync(user))
        {
            return BadRequest("Account is temporarily locked. Please try again later.");
        }

        // Validate password
        var result = await _signInManager.CheckPasswordSignInAsync(user, request.Password, true);
        if (!result.Succeeded)
        {
            // Failed login attempt is tracked by Identity framework
            return Unauthorized("Invalid credentials");
        }

        // Reset lockout count on successful login
        await _userManager.ResetAccessFailedCountAsync(user);

        // Generate new tokens
        var token = _jwtService.GenerateToken(user.Id, user.Email!, user.Role.ToString());
        var refreshToken = _jwtService.GenerateRefreshToken();
        var tokenExpiry = DateTime.UtcNow.AddSeconds(int.Parse(_config["Jwt:TokenTtlSeconds"] ?? "900"));
        var refreshTokenExpiry = DateTime.UtcNow.AddSeconds(int.Parse(_config["Jwt:RefreshTokenTtlSeconds"] ?? "604800"));

        // Invalidate any existing refresh tokens
        user.RefreshToken = refreshToken;
        user.RefreshTokenExpiry = refreshTokenExpiry;
        user.UpdatedAt = DateTime.UtcNow;

        var updateResult = await _userManager.UpdateAsync(user);
        if (!updateResult.Succeeded)
        {
            return StatusCode(500, "Failed to update user session");
        }

        // Set access token in HTTP-only cookie
        Response.Cookies.Append("accessToken", token, new CookieOptions
        {
            HttpOnly = true,
            Secure = true,
            SameSite = SameSiteMode.Strict,
            Expires = tokenExpiry
        });

        // Set refresh token in HTTP-only cookie
        Response.Cookies.Append("refreshToken", refreshToken, new CookieOptions
        {
            HttpOnly = true,
            Secure = true,
            SameSite = SameSiteMode.Strict,
            Expires = refreshTokenExpiry
        });

        return Ok(new AuthResponseDto
        {
            User = new RequestUserDto
            {
                Id = user.Id,
                Email = user.Email!,
                Role = user.Role.ToString()
            },
            Token = token,
            RefreshToken = refreshToken  // TODO: See real-world implementation if this was returned
        });
    }

    [HttpPost("refresh")]
    public async Task<IActionResult> Refresh([FromBody] RefreshTokenRequestDto request)
    {
        var user = await _userManager.FindByEmailAsync(request.Email);

        if (user == null || user.RefreshToken != request.RefreshToken || user.RefreshTokenExpiry <= DateTime.UtcNow)
        {
            return Unauthorized("Invalid refresh token");
        }

        var token = _jwtService.GenerateToken(user.Id, user.Email!, user.Role.ToString());
        var newRefreshToken = _jwtService.GenerateRefreshToken();

        user.RefreshToken = newRefreshToken;
        user.RefreshTokenExpiry = DateTime.UtcNow.AddSeconds(int.Parse(_config["Jwt:RefreshTokenTtlSeconds"] ?? "604800"));

        await _userManager.UpdateAsync(user);

        return Ok(new AuthResponseDto
        {
            User = new RequestUserDto
            {
                Id = user.Id,
                Email = user.Email!,
                Role = user.Role.ToString()
            },
            Token = token,
            RefreshToken = newRefreshToken
        });
    }

    [HttpPost("logout")]
    [Authorize]
    public async Task<IActionResult> Logout()
    {
        var userId = User.FindFirst("user_id")?.Value;

        if (userId != null && Guid.TryParse(userId, out var id))
        {
            var user = await _userManager.FindByIdAsync(id.ToString());

            if (user != null)
            {
                user.RefreshToken = null;
                user.RefreshTokenExpiry = null;
                await _userManager.UpdateAsync(user);
            }
        }

        return Ok(new
        {
            message = "Logged out successfully"
        });
    }

    [HttpGet("profile")]
    [Authorize]
    public async Task<IActionResult> GetProfile()
    {
        var userId = User.FindFirst("user_id")?.Value;

        if (userId == null || !Guid.TryParse(userId, out var id))
        {
            return Unauthorized();
        }

        var user = await _userManager.FindByIdAsync(id.ToString());
        if (user == null)
        {
            return NotFound("User not found");
        }

        return Ok(new RequestUserDto
        {
            Id = user.Id,
            Email = user.Email!,
            Role = user.Role.ToString()
        });
    }

    [HttpPut("profile")]
    [Authorize]
    public async Task<IActionResult> UpdateProfile([FromBody] UpdateProfileRequestDto request)
    {
        var userId = User.FindFirst("user_id")?.Value;

        if (userId == null || !Guid.TryParse(userId, out var id))
        {
            return Unauthorized();
        }

        var user = await _userManager.FindByIdAsync(id.ToString());

        if (user == null)
        {
            return NotFound("User not found");
        }

        user.FirstName = request.FirstName ?? user.FirstName;
        user.LastName = request.LastName ?? user.LastName;
        user.Bio = request.Bio ?? user.Bio;
        user.Avatar = request.Avatar ?? user.Avatar;
        user.UpdatedAt = DateTime.UtcNow;

        var result = await _userManager.UpdateAsync(user);

        if (!result.Succeeded)
        {
            return BadRequest(result.Errors);
        }

        return Ok(new UserDto
        {
            Id = user.Id,
            Email = user.Email!,
            FirstName = user.FirstName,
            LastName = user.LastName,
            Role = user.Role.ToString(),
            Bio = user.Bio,
            Avatar = user.Avatar
        });
    }
}
