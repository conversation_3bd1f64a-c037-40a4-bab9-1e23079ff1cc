{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=OnFlowCMS_db;Username=********;Password=********"}, "Jwt": {"SigningKey": "IJY4UWg1leKrakHXnnw6HqqVlqKqb/WZiE+0JgG2TWZd8WKgXcgvLPTCbhhJJtLh2me39mcGp3UpG/XUQiej/Q==", "Issuer": "OnFlowCMS", "Audience": "OnFlowCMSAudience", "TokenTtlSeconds": "900", "RefreshTokenTtlSeconds": "604800"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": "Information", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "logs/log-.txt", "rollingInterval": "Day"}}]}, "AllowedHosts": "*"}