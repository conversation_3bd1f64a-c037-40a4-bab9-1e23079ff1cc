﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OnFlowCMS.Api", "OnFlowCMS.Api\OnFlowCMS.Api.csproj", "{DDEF282A-7D2D-4AFB-A1F9-C356A9EDAF64}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OnFlowCMS.Application", "OnFlowCMS.Application\OnFlowCMS.Application.csproj", "{77AC9C9A-AFB2-4A3A-BABE-7EBFC4EFEF83}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OnFlowCMS.Domain", "OnFlowCMS.Domain\OnFlowCMS.Domain.csproj", "{B91A757B-AA71-42FF-8F77-3F64D0DF5496}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OnFlowCMS.Infrastructure", "OnFlowCMS.Infrastructure\OnFlowCMS.Infrastructure.csproj", "{63F4EA79-1B23-49D9-83EB-A3C2266C25BF}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DDEF282A-7D2D-4AFB-A1F9-C356A9EDAF64}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DDEF282A-7D2D-4AFB-A1F9-C356A9EDAF64}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DDEF282A-7D2D-4AFB-A1F9-C356A9EDAF64}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DDEF282A-7D2D-4AFB-A1F9-C356A9EDAF64}.Debug|x64.Build.0 = Debug|Any CPU
		{DDEF282A-7D2D-4AFB-A1F9-C356A9EDAF64}.Debug|x86.ActiveCfg = Debug|Any CPU
		{DDEF282A-7D2D-4AFB-A1F9-C356A9EDAF64}.Debug|x86.Build.0 = Debug|Any CPU
		{DDEF282A-7D2D-4AFB-A1F9-C356A9EDAF64}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DDEF282A-7D2D-4AFB-A1F9-C356A9EDAF64}.Release|Any CPU.Build.0 = Release|Any CPU
		{DDEF282A-7D2D-4AFB-A1F9-C356A9EDAF64}.Release|x64.ActiveCfg = Release|Any CPU
		{DDEF282A-7D2D-4AFB-A1F9-C356A9EDAF64}.Release|x64.Build.0 = Release|Any CPU
		{DDEF282A-7D2D-4AFB-A1F9-C356A9EDAF64}.Release|x86.ActiveCfg = Release|Any CPU
		{DDEF282A-7D2D-4AFB-A1F9-C356A9EDAF64}.Release|x86.Build.0 = Release|Any CPU
		{77AC9C9A-AFB2-4A3A-BABE-7EBFC4EFEF83}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{77AC9C9A-AFB2-4A3A-BABE-7EBFC4EFEF83}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{77AC9C9A-AFB2-4A3A-BABE-7EBFC4EFEF83}.Debug|x64.ActiveCfg = Debug|Any CPU
		{77AC9C9A-AFB2-4A3A-BABE-7EBFC4EFEF83}.Debug|x64.Build.0 = Debug|Any CPU
		{77AC9C9A-AFB2-4A3A-BABE-7EBFC4EFEF83}.Debug|x86.ActiveCfg = Debug|Any CPU
		{77AC9C9A-AFB2-4A3A-BABE-7EBFC4EFEF83}.Debug|x86.Build.0 = Debug|Any CPU
		{77AC9C9A-AFB2-4A3A-BABE-7EBFC4EFEF83}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{77AC9C9A-AFB2-4A3A-BABE-7EBFC4EFEF83}.Release|Any CPU.Build.0 = Release|Any CPU
		{77AC9C9A-AFB2-4A3A-BABE-7EBFC4EFEF83}.Release|x64.ActiveCfg = Release|Any CPU
		{77AC9C9A-AFB2-4A3A-BABE-7EBFC4EFEF83}.Release|x64.Build.0 = Release|Any CPU
		{77AC9C9A-AFB2-4A3A-BABE-7EBFC4EFEF83}.Release|x86.ActiveCfg = Release|Any CPU
		{77AC9C9A-AFB2-4A3A-BABE-7EBFC4EFEF83}.Release|x86.Build.0 = Release|Any CPU
		{B91A757B-AA71-42FF-8F77-3F64D0DF5496}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B91A757B-AA71-42FF-8F77-3F64D0DF5496}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B91A757B-AA71-42FF-8F77-3F64D0DF5496}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B91A757B-AA71-42FF-8F77-3F64D0DF5496}.Debug|x64.Build.0 = Debug|Any CPU
		{B91A757B-AA71-42FF-8F77-3F64D0DF5496}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B91A757B-AA71-42FF-8F77-3F64D0DF5496}.Debug|x86.Build.0 = Debug|Any CPU
		{B91A757B-AA71-42FF-8F77-3F64D0DF5496}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B91A757B-AA71-42FF-8F77-3F64D0DF5496}.Release|Any CPU.Build.0 = Release|Any CPU
		{B91A757B-AA71-42FF-8F77-3F64D0DF5496}.Release|x64.ActiveCfg = Release|Any CPU
		{B91A757B-AA71-42FF-8F77-3F64D0DF5496}.Release|x64.Build.0 = Release|Any CPU
		{B91A757B-AA71-42FF-8F77-3F64D0DF5496}.Release|x86.ActiveCfg = Release|Any CPU
		{B91A757B-AA71-42FF-8F77-3F64D0DF5496}.Release|x86.Build.0 = Release|Any CPU
		{63F4EA79-1B23-49D9-83EB-A3C2266C25BF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{63F4EA79-1B23-49D9-83EB-A3C2266C25BF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{63F4EA79-1B23-49D9-83EB-A3C2266C25BF}.Debug|x64.ActiveCfg = Debug|Any CPU
		{63F4EA79-1B23-49D9-83EB-A3C2266C25BF}.Debug|x64.Build.0 = Debug|Any CPU
		{63F4EA79-1B23-49D9-83EB-A3C2266C25BF}.Debug|x86.ActiveCfg = Debug|Any CPU
		{63F4EA79-1B23-49D9-83EB-A3C2266C25BF}.Debug|x86.Build.0 = Debug|Any CPU
		{63F4EA79-1B23-49D9-83EB-A3C2266C25BF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{63F4EA79-1B23-49D9-83EB-A3C2266C25BF}.Release|Any CPU.Build.0 = Release|Any CPU
		{63F4EA79-1B23-49D9-83EB-A3C2266C25BF}.Release|x64.ActiveCfg = Release|Any CPU
		{63F4EA79-1B23-49D9-83EB-A3C2266C25BF}.Release|x64.Build.0 = Release|Any CPU
		{63F4EA79-1B23-49D9-83EB-A3C2266C25BF}.Release|x86.ActiveCfg = Release|Any CPU
		{63F4EA79-1B23-49D9-83EB-A3C2266C25BF}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
