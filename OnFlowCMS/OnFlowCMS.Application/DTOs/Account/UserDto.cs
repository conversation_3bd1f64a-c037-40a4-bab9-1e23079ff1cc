using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using OnFlowCMS.Domain.Enums;

namespace OnFlowCMS.Application.DTOs.Account
{
    public class UserDto
    {
        public Guid Id { get; set; }
        public string Email { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Bio { get; set; } = string.Empty;
        public string Avatar { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
    }
}