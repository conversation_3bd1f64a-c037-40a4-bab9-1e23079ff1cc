{"version": 2, "dgSpecHash": "ZW+KS2cxYT0=", "success": true, "projectFilePath": "/home/<USER>/hotzone/onflow/api/OnFlowCMS/OnFlowCMS.Infrastructure/OnFlowCMS.Infrastructure.csproj", "expectedPackageFiles": ["/home/<USER>/.nuget/packages/automapper/15.0.1/automapper.15.0.1.nupkg.sha512", "/home/<USER>/.nuget/packages/efcore.namingconventions/9.0.0/efcore.namingconventions.9.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/fluentvalidation/12.0.0/fluentvalidation.12.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/humanizer.core/2.14.1/humanizer.core.2.14.1.nupkg.sha512", "/home/<USER>/.nuget/packages/mediatr/13.0.0/mediatr.13.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/mediatr.contracts/2.0.1/mediatr.contracts.2.0.1.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.aspnetcore.cryptography.internal/9.0.8/microsoft.aspnetcore.cryptography.internal.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.aspnetcore.cryptography.keyderivation/9.0.8/microsoft.aspnetcore.cryptography.keyderivation.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.aspnetcore.identity.entityframeworkcore/9.0.8/microsoft.aspnetcore.identity.entityframeworkcore.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.bcl.asyncinterfaces/7.0.0/microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.build.framework/17.8.3/microsoft.build.framework.17.8.3.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.build.locator/1.7.8/microsoft.build.locator.1.7.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.codeanalysis.analyzers/3.3.4/microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.codeanalysis.common/4.8.0/microsoft.codeanalysis.common.4.8.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.codeanalysis.csharp/4.8.0/microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.codeanalysis.csharp.workspaces/4.8.0/microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.codeanalysis.workspaces.common/4.8.0/microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.codeanalysis.workspaces.msbuild/4.8.0/microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.entityframeworkcore/9.0.8/microsoft.entityframeworkcore.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.entityframeworkcore.abstractions/9.0.8/microsoft.entityframeworkcore.abstractions.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.entityframeworkcore.analyzers/9.0.8/microsoft.entityframeworkcore.analyzers.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.entityframeworkcore.design/9.0.8/microsoft.entityframeworkcore.design.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.entityframeworkcore.relational/9.0.8/microsoft.entityframeworkcore.relational.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.caching.abstractions/9.0.8/microsoft.extensions.caching.abstractions.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.caching.memory/9.0.8/microsoft.extensions.caching.memory.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/9.0.8/microsoft.extensions.configuration.abstractions.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/9.0.8/microsoft.extensions.dependencyinjection.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/9.0.8/microsoft.extensions.dependencyinjection.abstractions.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.dependencymodel/9.0.8/microsoft.extensions.dependencymodel.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.identity.core/9.0.8/microsoft.extensions.identity.core.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.identity.stores/9.0.8/microsoft.extensions.identity.stores.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.logging/9.0.8/microsoft.extensions.logging.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/9.0.8/microsoft.extensions.logging.abstractions.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.options/9.0.8/microsoft.extensions.options.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.primitives/9.0.8/microsoft.extensions.primitives.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.identitymodel.abstractions/8.2.1/microsoft.identitymodel.abstractions.8.2.1.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.identitymodel.jsonwebtokens/8.2.1/microsoft.identitymodel.jsonwebtokens.8.2.1.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.identitymodel.logging/8.2.1/microsoft.identitymodel.logging.8.2.1.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.identitymodel.tokens/8.2.1/microsoft.identitymodel.tokens.8.2.1.nupkg.sha512", "/home/<USER>/.nuget/packages/mono.texttemplating/3.0.0/mono.texttemplating.3.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/npgsql/9.0.3/npgsql.9.0.3.nupkg.sha512", "/home/<USER>/.nuget/packages/npgsql.entityframeworkcore.postgresql/9.0.4/npgsql.entityframeworkcore.postgresql.9.0.4.nupkg.sha512", "/home/<USER>/.nuget/packages/slugify.core/5.1.1/slugify.core.5.1.1.nupkg.sha512", "/home/<USER>/.nuget/packages/system.codedom/6.0.0/system.codedom.6.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.collections.immutable/7.0.0/system.collections.immutable.7.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.composition/7.0.0/system.composition.7.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.composition.attributedmodel/7.0.0/system.composition.attributedmodel.7.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.composition.convention/7.0.0/system.composition.convention.7.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.composition.hosting/7.0.0/system.composition.hosting.7.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.composition.runtime/7.0.0/system.composition.runtime.7.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.composition.typedparts/7.0.0/system.composition.typedparts.7.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.identitymodel.tokens.jwt/8.2.1/system.identitymodel.tokens.jwt.8.2.1.nupkg.sha512", "/home/<USER>/.nuget/packages/system.io.pipelines/7.0.0/system.io.pipelines.7.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.memory/4.6.1/system.memory.4.6.1.nupkg.sha512", "/home/<USER>/.nuget/packages/system.reflection.metadata/7.0.0/system.reflection.metadata.7.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.runtime.compilerservices.unsafe/6.0.0/system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.text.encoding.codepages/9.0.3/system.text.encoding.codepages.9.0.3.nupkg.sha512", "/home/<USER>/.nuget/packages/system.text.json/9.0.8/system.text.json.9.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/system.threading.channels/7.0.0/system.threading.channels.7.0.0.nupkg.sha512"], "logs": []}