namespace OnFlowCMS.Domain.Entities;

public class Post : BaseEntity
{
    public string Title { get; set; } = string.Empty;
    public string Slug { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string? Excerpt { get; set; }
    public bool IsPublished { get; set; }
    public DateTime? PublishedAt { get; set; }

    public Guid AuthorId { get; set; }
    public virtual User Author { get; set; } = null!;
    public virtual ICollection<PostTag> PostTags { get; set; } = new List<PostTag>();
}
